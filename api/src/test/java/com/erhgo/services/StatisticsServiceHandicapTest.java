package com.erhgo.services;

import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.UserProfileRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StatisticsServiceHandicapTest {

    @Mock
    private UserProfileRepository userProfileRepository;

    @InjectMocks
    private StatisticsService statisticsService;

    @Test
    void testGetSeveralMetricsStats_WithHandicapMetrics() {
        // Given
        when(userProfileRepository.countUserProfilesWithModifiedAttitude()).thenReturn(10);
        when(userProfileRepository.countUserProfilesWithIsFromHandicap()).thenReturn(5);
        when(userProfileRepository.countUserProfilesWithHandicapModeEnabled()).thenReturn(8);
        when(userProfileRepository.countCandidaturesByHandicapEnabledUsers()).thenReturn(15);

        // When
        var result = statisticsService.getSeveralMetricsStats();

        // Then
        assertEquals(10, result.getUserProfilesWithModifiedAttitudeCount());
        assertEquals(5, result.getUserProfilesWithIsFromHandicapCount());
        assertEquals(8, result.getUserProfilesWithHandicapModeEnabledCount());
        assertEquals(15, result.getCandidaturesByHandicapEnabledUsersCount());
    }

    @Test
    void testHandicapFieldsAccess() {
        // Test that we can access the handicap fields correctly
        var userProfile = new UserProfile();
        userProfile.isFromHandicap(true);
        userProfile.handicapModeEnabled(false);

        assertEquals(true, userProfile.isFromHandicap());
        assertEquals(false, userProfile.handicapModeEnabled());
    }
}
