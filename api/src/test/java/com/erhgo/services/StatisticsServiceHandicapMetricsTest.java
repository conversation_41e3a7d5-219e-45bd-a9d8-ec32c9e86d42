package com.erhgo.services;

import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.UserProfileRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StatisticsServiceHandicapMetricsTest {

    @Mock
    private UserProfileRepository userProfileRepository;

    @InjectMocks
    private StatisticsService statisticsService;

    @Test
    void testGetSeveralMetricsStats_WithHandicapMetrics() {
        // Given
        when(userProfileRepository.countUserProfilesWithModifiedAttitude()).thenReturn(10);
        when(userProfileRepository.countUserProfilesWithIsFromHandicap()).thenReturn(5);
        when(userProfileRepository.countUserProfilesWithHandicapModeEnabled()).thenReturn(8);
        when(userProfileRepository.countCandidaturesByHandicapEnabledUsers()).thenReturn(15);

        // When
        var result = statisticsService.getSeveralMetricsStats();

        // Then
        assertEquals(10, result.getUserProfilesWithModifiedAttitudeCount());
        assertEquals(5, result.getUserProfilesWithIsFromHandicapCount());
        assertEquals(8, result.getUserProfilesWithHandicapModeEnabledCount());
        assertEquals(15, result.getCandidaturesByHandicapEnabledUsersCount());
    }

    @Test
    void testUserProfile_MutualizedMethods() {
        // Test des méthodes mutualisées dans UserProfile
        var userProfile = new UserProfile();
        var prescriber = new UserChannel()
                .channel("S-TEST")
                .channelSourceType(UserChannel.ChannelSourceType.ORGANIZATION);
        userProfile.prescriber(prescriber);

        var organizationTitleForCode = Map.of("S-TEST", "Test Organization");

        // Test getPrescriberLabel
        var prescriberLabel = userProfile.getPrescriberLabel(organizationTitleForCode);
        assertEquals("Test Organization (S-TEST)", prescriberLabel);

        // Test getChannelSourceTypeText
        var sourceText = userProfile.getChannelSourceTypeText();
        assertEquals("Organisation", sourceText);
    }

    @Test
    void testUserProfile_MutualizedMethods_WithNullValues() {
        // Test avec des valeurs nulles
        var userProfile = new UserProfile();
        var organizationTitleForCode = Map.of("S-TEST", "Test Organization");

        // Test getPrescriberLabel avec prescriber vide
        var prescriberLabel = userProfile.getPrescriberLabel(organizationTitleForCode);
        assertEquals("Aucun", prescriberLabel);

        // Test getChannelSourceTypeText avec source type null
        var sourceText = userProfile.getChannelSourceTypeText();
        assertEquals("Inconnu", sourceText);
    }
}
