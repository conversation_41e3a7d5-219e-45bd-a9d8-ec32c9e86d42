package com.erhgo.repositories;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.userprofile.MailConfiguration;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.repositories.dto.UserMatchingSummary;
import com.erhgo.repositories.dto.UserProfileProgress;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.time.OffsetDateTime;
import java.util.*;

public interface UserProfileRepository extends JpaRepository<UserProfile, UUID> {

    String SELECT_USER_PROFILE_PROGRESS =
            """
                    SELECT user                                                                     AS userProfile,
                           COUNT(DISTINCT capacities)                                               as capacitiesCount,
                           COUNT(DISTINCT experiences)                                              as experiencesCount,
                           COUNT(behaviors) > 0                                                     as hasBehaviors,
                           (select count(cand.id)
                            FROM RecruitmentCandidature cand
                                     INNER JOIN cand.recruitment R
                                     INNER JOIN R.recruitmentProfile RP
                                     INNER JOIN RP.job j
                                     INNER JOIN j.recruiter rec
                            WHERE cand.userProfile = user
                              AND (:isAdmin = TRUE OR rec.code in (:organizationsCodes))
                              AND (cand.state = 4)) as candidaturesCount,
                           user.masteryLevel                                                        as masteryLevel
                    FROM UserProfile user
                             LEFT JOIN user.generalInformation as generalInformation
                             LEFT JOIN generalInformation.location as location
                        LEFT JOIN user.userRegistrationState
                        LEFT JOIN user.experiences as experiences
                        LEFT JOIN user.behaviors as behaviors
                        LEFT JOIN CapacityOccurrence capacities
                    ON capacities.userProfile = user AND capacities.occurrence > 0
                    """;

    Optional<UserProfile> findByUserId(String userId);

    List<UserProfile> findByUserIdIn(Collection<String> usersId);

    @Query(value = "" +
            "select distinct up.userId as userId, " +
            "   g.phoneNumber as phone, " +
            "   g.city as city, " +
            "   g.postcode as postcode, " +
            "   g.phoneNumber as phoneNumber, " +
            "   g.contactTime as contactTimeOrdinal," +
            "   g.salary as salary, " +
            "   g.situation as situation, " +
            "   g.smsBlacklisted as smsBlacklisted, " +
            "   c.id as candidatureId, " +
            "   up.jobOfferOptOut as transactionalBlacklisted, " +
            "   up.source as source, " +
            "   GROUP_CONCAT(DISTINCT ch.channel) as channelsAsStringFromDB, " +
            "   COUNT (DISTINCT capacities.capacity_id) as numberOfCapacities, " +
            "   COALESCE(criteriaSubquery.matchingCriteriaScore, 0) as matchingCriteriaScore, " +
            "   COALESCE(criteriaSubquery.missingCriteriaScore, 0) as missingCriteriaScore, " +
            "   criteriaSubquery.missingCriteria as missingCriteriaAsStringFromDB, " +
            "   criteriaSubquery.matchingCriteria as matchingCriteriaAsStringFromDB " +
            "    from " +
            "        UserProfile up  " +
            "    LEFT OUTER JOIN " +
            "        ChannelAffectation ch " +
            "            on ch.userProfile_uuid = up.uuid" +
            "    LEFT OUTER join " +
            "        GeneralInformation g " +
            "            on g.userProfile_uuid = up.uuid " +
            "    LEFT OUTER JOIN " +
            "        RecruitmentCandidature c " +
            "           ON c.userProfile_uuid = up.uuid AND c.recruitment_id IN (:#{#criteria.recruitmentIds})" +
            "   LEFT JOIN (SELECT " +
            "        SUM (" +
            "           CASE " +
            "               WHEN crit.questionType = 'THRESHOLD' AND userCV.valueIndex >= cv.valueIndex THEN 1" +
            "               WHEN crit.questionType = 'MULTIPLE' AND ucv.value_code = cv.code AND ucv.selected IS TRUE THEN 1 " +
            "               ELSE 0 " +
            "           END) as matchingCriteriaScore, " +
            "        SUM (" +
            "           CASE " +
            "               WHEN crit.questionType = 'THRESHOLD' AND userCV.valueIndex < cv.valueIndex THEN -1" +
            "               WHEN crit.questionType = 'MULTIPLE' AND ucv.value_code = cv.code AND ucv.selected IS FALSE THEN -1 " +
            "               ELSE 0 " +
            "           END) as missingCriteriaScore, " +
            "        GROUP_CONCAT(" +
            "               CASE " +
            "                   WHEN crit.questionType = 'THRESHOLD' AND userCV.valueIndex < cv.valueIndex THEN cv.code " +
            "                   WHEN crit.questionType = 'MULTIPLE' AND ucv.value_code = cv.code AND ucv.selected IS FALSE THEN cv.code " +
            "                    " +
            "               END) as missingCriteria," +
            "        GROUP_CONCAT(" +
            "               CASE " +
            "               WHEN crit.questionType = 'MULTIPLE' AND ucv.value_code = cv.code AND ucv.selected IS TRUE THEN cv.code " +
            "               WHEN crit.questionType = 'THRESHOLD' AND userCV.valueIndex >= cv.valueIndex THEN cv.code " +
            "               END) as matchingCriteria," +
            "        ucv.userProfile_uuid as userProfile_uuid  " +
            "        FROM CriteriaValue cv " +
            "        INNER JOIN Criteria crit ON crit.code = cv.criteria_code " +
            "        INNER JOIN UserCriteriaValue ucv ON ucv.value_code IN (SELECT code FROM CriteriaValue WHERE criteria_code = crit.code) " +
            "        INNER JOIN CriteriaValue userCV ON ucv.value_code = userCV.code " +
            "        WHERE cv.code in (:#{#criteria.criteriaValueCodes}) " +
            "        GROUP BY ucv.userProfile_uuid " +
            "    ) criteriaSubquery ON criteriaSubquery.userProfile_uuid = up.uuid" +
            "   INNER JOIN " +
            "        CapacityOccurrence capacities " +
            "           ON up.uuid = capacities.userProfile_uuid " +
            "   WHERE capacities.capacity_id IN (:#{#criteria.capacitiesIds}) " +
            "   AND (:#{#criteria.postcode} IS NULL OR g.postcode LIKE CONCAT(:#{#criteria.postcode}, '%')) " +
            "   AND (:#{#criteria.noChannel} IS TRUE OR " +
            "           EXISTS (" +
            "               SELECT * " +
            "               from ChannelAffectation ch2 " +
            "               WHERE ch2.channel IN (:#{#criteria.channels}) " +
            "               AND ch2.UserProfile_uuid = up.uuid" +
            "       ) OR (:#{#criteria.affectedToNoChannel} IS TRUE AND ( " +
            "           NOT EXISTS (" +
            "               SELECT * " +
            "               from ChannelAffectation ch2 " +
            "               WHERE  ch2.UserProfile_uuid = up.uuid " +
            "           )" +
            "       ))) " +
            "   AND (NOT EXISTS(SELECT * FROM ChannelAffectation ch3 WHERE ch3.UserProfile_uuid = up.uuid AND ch3.channel in (:#{#criteria.excludedChannels})))" +
            "   AND (GREATEST(up.masteryLevel, 1) - 1 + :#{#criteria.masteryLevelRange}) >= :#{#criteria.minLevel} " +
            "   GROUP BY up.userId  " +
            "   HAVING " +
            "       COUNT (DISTINCT capacities.capacity_id) >= (:#{#criteria.capacityThreshold}/100) * :#{#criteria.capacitiesLength} " +
            " ORDER BY matchingCriteriaScore desc, missingCriteriaScore desc, numberOfCapacities desc " +
            " LIMIT  :#{#criteria.limit} " +
            " OFFSET :#{#criteria.offset} "
            , nativeQuery = true)
    List<UserMatchingSummary> getUserMatchingCapacities(UserMatchingCapacitiesCriteria criteria);


    @Query(value = """
            select count(users.userId) from (
                        select distinct up.userId as userId from UserProfile up
                        LEFT OUTER JOIN ChannelAffectation ch
                            on ch.userProfile_uuid = up.uuid
                        LEFT OUTER join GeneralInformation g
                            on g.userProfile_uuid = up.uuid
                        LEFT OUTER JOIN RecruitmentCandidature c
                            ON c.userProfile_uuid = up.uuid
                            AND c.recruitment_id IN (:#{#criteria.recruitmentIds})
                        INNER JOIN CapacityOccurrence capacities
                            ON up.uuid = capacities.userProfile_uuid
                        WHERE
                            capacities.capacity_id IN (:#{#criteria.capacitiesIds})
                        AND (
                                :#{#criteria.postcode} IS NULL
                                OR g.postcode LIKE CONCAT(:#{#criteria.postcode}, '%')
                            )
                        AND (
                                :#{#criteria.noChannel} IS TRUE
                                OR EXISTS (
                                    SELECT * from ChannelAffectation ch2
                                    WHERE ch2.channel IN (:#{#criteria.channels})
                                AND ch2.UserProfile_uuid = up.uuid
                                )
                                OR (
                                    :#{#criteria.affectedToNoChannel} IS TRUE
                                    AND (
                                        NOT EXISTS (
                                            SELECT 1 FROM ChannelAffectation ch2 WHERE  ch2.UserProfile_uuid = up.uuid
                                        )
                                    )
                                )
                        )
                        AND (GREATEST(up.masteryLevel, 1) - 1 + :#{#criteria.masteryLevelRange}) >= :#{#criteria.minLevel}
                        GROUP BY up.userId
                        HAVING COUNT(DISTINCT capacities.capacity_id) >= (:#{#criteria.capacityThreshold}/100) * :#{#criteria.capacitiesLength}
            ) as users
            """
            , nativeQuery = true)
    long countUserMatchingCapacities(UserMatchingCapacitiesCriteria criteria);

    @Query(SELECT_USER_PROFILE_PROGRESS +
            " WHERE :exportAll =TRUE or user.userId IN :usersId " +
            " AND (:postcode IS NULL OR location.postcode LIKE CONCAT(:postcode, '%')) " +
            " GROUP BY user.uuid")
    List<UserProfileProgress> getUserProfilesProgress(Collection<String> usersId,
                                                      Collection<String> organizationsCodes,
                                                      Boolean isAdmin,
                                                      Boolean exportAll,
                                                      String postcode);

    // Next query provokes a silently stacktrace (level info) in hibernate because isAdmin param is not used in count query
    @Query(value = SELECT_USER_PROFILE_PROGRESS +
            """
                    LEFT JOIN user.affectationsHistory as affectations
                    WHERE affectations.userChannel.channel IN (:organizationsCodes)
                    AND (:postcode IS NULL OR location.postcode LIKE CONCAT(:postcode, '%'))
                    GROUP BY user.uuid
                    """,
            countQuery =
                    """
                            SELECT COUNT(distinct user)
                            FROM UserProfile user
                            LEFT JOIN user.affectationsHistory as affectations
                            LEFT JOIN user.generalInformation as generalInformation
                            LEFT JOIN generalInformation.location as location
                            WHERE affectations.userChannel.channel IN (:organizationsCodes)
                            AND (:postcode IS NULL OR location.postcode LIKE CONCAT(:postcode, '%'))
                            """

    )
    Page<UserProfileProgress> getUserProfilesProgressByChannels(Set<String> organizationsCodes,
                                                                Boolean isAdmin,
                                                                Pageable pageable,
                                                                String postcode);


    List<UserProfile> findByAffectationsHistoryUserChannelChannelIn(Set<String> channels);


    @Query("SELECT up " +
            "FROM UserProfile up " +
            "WHERE up.indexationRequiredDate < :indexationRequiredDateMax " +
            "ORDER BY up.lastIndexationDate ASC NULLS FIRST "
    )
    Page<UserProfile> findUsersToIndex(Date indexationRequiredDateMax, Pageable pageable);

    @Query("""
            SELECT DISTINCT up
            FROM UserProfile  up
            LEFT JOIN up.generalInformation gi  
            """)
    List<UserProfile> findAllFOUsers();

    @Query("""
            SELECT DISTINCT up
            FROM UserProfile up
            JOIN up.generalInformation gi
            JOIN gi.mailVerificationState mvs
            JOIN up.prescriber pr
            WHERE up.mailConfiguration.confirmationMailSent = com.erhgo.domain.userprofile.MailConfiguration$ConfirmationMailState.NEW
            AND up.createdDate <= :date
            AND pr.channelSourceType != com.erhgo.domain.userprofile.UserChannel$ChannelSourceType.CREATION_BO
            AND mvs.state != com.erhgo.domain.userprofile.MailVerification$MailVerificationState.REQUIRES_VERIFICATION
            """)
    List<UserProfile> findNewUsersRequiringWelcomeEmail(Date date);

    List<UserProfile> findByMailConfigurationConfirmationMailSentAndCreatedDateBefore(MailConfiguration.ConfirmationMailState state, Date date);

    List<UserProfile> findByUserRegistrationStateSelectedOccupation(ErhgoOccupation occupation);

    @Modifying
    @Query("UPDATE UserProfile SET trimojiStatus.endedDate=:now WHERE userId=:userId")
    void endSoftSkillTest(String userId, OffsetDateTime now);

    @Modifying
    @Query("UPDATE UserProfile SET trimojiStatus.trimojiPdfUrl=:url WHERE userId=:userId")
    void setSoftSkillsUrl(String userId, String url);


    @Query("SELECT count(up) FROM UserProfile up WHERE up.userExperienceGeneratedData.attitudeModifiedByUserInstant IS NOT NULL")
    Integer countUserProfilesWithModifiedAttitude();

    @Query("SELECT count(up) FROM UserProfile up WHERE up.isFromHandicap = true")
    Integer countUserProfilesWithIsFromHandicap();

    @Query("SELECT count(up) FROM UserProfile up WHERE up.handicapModeEnabled = true")
    Integer countUserProfilesWithHandicapModeEnabled();

    @Query("""
            SELECT count(DISTINCT c) FROM RecruitmentCandidature c
            INNER JOIN c.userProfile up
            WHERE up.handicapModeEnabled = true
            AND c.archived = false
            """)
    Integer countCandidaturesByHandicapEnabledUsers();

    @Query("SELECT up FROM UserProfile up WHERE up.isFromHandicap = true OR up.handicapModeEnabled = true")
    List<UserProfile> findHandicapUsers();
}
