package com.erhgo.services;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.UserChannel;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.utils.StringUtils;
import com.google.common.base.Joiner;
import com.opencsv.CSVWriter;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.StringWriter;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.opencsv.ICSVWriter.*;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final AbstractOrganizationRepository organizationRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final KeycloakService keycloakService;
    private final RecruitmentRepository recruitmentRepository;
    private final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    private final UserProfileRepository userProfileRepository;
    private final RecruiterRepository recruiterRepository;


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public MonthlyCandidaturesStatsDTO getMonthlyCandidaturesStats() {
        return new MonthlyCandidaturesStatsDTO()
                .rhoneAlpesStats(mapToMonthlyCandidaturesDTO(recruitmentCandidatureRepository.getMonthlyApplicationsStats(true)))
                .notRhoneAlpesStats(mapToMonthlyCandidaturesDTO(recruitmentCandidatureRepository.getMonthlyApplicationsStats(false)));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public MonthlySpontaneousCandidaturesStatsDTO getMonthlySpontaneousCandidaturesStats() {
        var stats = spontaneousCandidatureRepository.getMonthlySpontaneousCandidaturesStats();
        var dtoList = stats.stream()
                .map(stat -> new MonthlySpontaneousCandidaturesStatsItemDTO()
                        .month(stat.getMonth())
                        .totalSpontaneousCandidatureCount(stat.getTotalCandidatureCount()))
                .toList();
        return new MonthlySpontaneousCandidaturesStatsDTO()
                .spontaneousCandidaturesStats(dtoList);
    }


    private List<MonthlyCandidaturesDTO> mapToMonthlyCandidaturesDTO(List<RecruitmentCandidatureRepository.MonthlyCandidaturesDTO> rawStats) {
        return rawStats.stream()
                .map(raw -> new MonthlyCandidaturesDTO()
                        .month(raw.getMonth())
                        .averageCandidatureCount(raw.getAverageCandidatureCount())
                        .averageCandidatureCountExcludingZero(raw.getAverageCandidatureCountExcludingZero())
                        .totalRecruitmentCount(raw.getTotalRecruitmentCount())
                        .totalCandidatureCount(raw.getTotalCandidatureCount())
                        .totalCandidatureWithNotificationCount(raw.getTotalCandidatureWithNotificationCount())
                )
                .toList();
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentStatsDTO getRecruitmentStats() {
        log.debug("Retrieving recruitment stats");
        var recruitmentStats = recruitmentRepository.getRecruitmentStats();
        var sourcingPrefix = AbstractOrganization.OrganizationType.SOURCING.getPrefix();
        var projectPrefix = AbstractOrganization.OrganizationType.PROJECT.getPrefix();
        var rolesPerGroup = keycloakService.searchBOGroupsWithRoles(sourcingPrefix);
        var projectsCodes = rolesPerGroup.values().stream().flatMap(Collection::stream).filter(a -> StringUtils.startsWith(a, projectPrefix)).toList();
        var projectPerCodes = organizationRepository.findByCodeIn(projectsCodes).stream().collect(Collectors.toMap(AbstractOrganization::getCode, Function.identity()));
        var organizations = recruitmentStats.stream()
                .map(data -> new RecruitmentStatsItemDTO()
                        .organizationTitle(data.getOrganizationTitle())
                        .organizationCode(data.getOrganizationCode())
                        .lastRecruitmentPublicationDate(data.getLastRecruitmentPublicationDate() != null ? OffsetDateTime.ofInstant(data.getLastRecruitmentPublicationDate(), ZoneId.systemDefault()) : null)
                        .totalCandidatures(data.getTotalCandidatures())
                        .totalUsersInChannel(data.getTotalUsersInChannel())
                        .totalCandidaturesArchived(data.getTotalCandidaturesArchived())
                        .spontaneousCandidaturesArchived(data.getSpontaneousCandidaturesArchived())
                        .openRecruitmentsCount(data.getOpenRecruitmentsCount())
                        .closedRecruitmentsCount(data.getClosedRecruitmentsCount())
                        .totalTransmittedCandidatures(data.getTotalTransmittedCandidatures())
                        .selectedCandidaturesCount(data.getSelectedCandidaturesCount())
                        .contactedCandidaturesCount(data.getContactedCandidaturesCount())
                        .refusedCandidaturesCount(data.getRefusedCandidaturesCount())
                        .spontaneousCandidaturesCount(data.getSpontaneousCandidaturesCount())
                        .connectedAts(data.getConnectedAts())
                        .projects(getProjects(data, rolesPerGroup, projectPerCodes))
                )
                .toList();

        log.debug("Retrieved {} recruitment stats", organizations.size());
        return new RecruitmentStatsDTO().organizations(organizations);
    }

    private String getProjects(com.erhgo.repositories.dto.RecruitmentStatsDTO data, Map<String, Set<String>> rolesPerGroup, Map<String, AbstractOrganization> projectPerCodes) {
        var rolesForGroup = rolesPerGroup.get(data.getOrganizationCode());
        var none = "<aucun>";
        if (rolesForGroup == null) {
            return none;
        }
        var projectTitlesForGroup = rolesForGroup.stream()
                .filter(a -> a.startsWith(AbstractOrganization.OrganizationType.PROJECT.getPrefix()))
                .map(projectPerCodes::get)
                .filter(Objects::nonNull)
                .map(AbstractOrganization::getTitle)
                .toList();
        if (projectTitlesForGroup.isEmpty()) {
            return none;
        }
        return Joiner.on(", ").join(projectTitlesForGroup);
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public SeveralMetricsStatsDTO getSeveralMetricsStats() {
        return new SeveralMetricsStatsDTO()
                .userProfilesWithModifiedAttitudeCount(userProfileRepository.countUserProfilesWithModifiedAttitude())
                .userProfilesWithIsFromHandicapCount(userProfileRepository.countUserProfilesWithIsFromHandicap())
                .userProfilesWithHandicapModeEnabledCount(userProfileRepository.countUserProfilesWithHandicapModeEnabled())
                .candidaturesByHandicapEnabledUsersCount(userProfileRepository.countCandidaturesByHandicapEnabledUsers());
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public String exportHandicapAccountsData() {
        log.debug("Exporting handicap accounts data");

        // Mutualisation du code existant pour organizationTitleForCode
        var organizationTitleForCode = StreamSupport.stream(recruiterRepository.findAll().spliterator(), false)
                .collect(Collectors.toMap(Recruiter::getCode, Recruiter::getTitle));

        var handicapUsers = userProfileRepository.findHandicapUsers();

        var writer = new StringWriter();
        try (var csvWriter = new CSVWriter(
                writer,
                StringUtils.CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {

            // Headers avec les 6 colonnes demandées
            csvWriter.writeNext(new String[]{
                    "Identifiant",
                    "Date de création",
                    "Prescripteur",
                    "Source",
                    "jenesuispasunhandicap (origine) O/N",
                    "jenesuispasunhandicap (actif) O/N"
            });

            // Récupération des utilisateurs Keycloak pour les dates de création
            var userRepresentations = handicapUsers.stream()
                    .map(up -> keycloakService.getFrontOfficeUserProfile(up.getUserId()))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toMap(ur -> ur.getId(), Function.identity()));

            // Write data
            for (UserProfile userProfile : handicapUsers) {
                var userRepresentation = userRepresentations.get(userProfile.getUserId());

                // Mutualisation du code prescriber (même pattern que GeneralInformationService)
                var prescriberCode = ofNullable(userProfile.prescriber().channel());
                var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
                var prescriberLabel = prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse("Aucun");

                // Mutualisation du code source (même pattern que GeneralInformationService)
                var source = ofNullable(userProfile.prescriber().channelSourceType())
                        .map(UserChannel.ChannelSourceType::text)
                        .orElse(UserChannel.ChannelSourceType.UNKNOWN.text());

                csvWriter.writeNext(new String[]{
                        userProfile.getUserId(),
                        userRepresentation != null ? StringUtils.formatTimestamp(userRepresentation.getCreatedTimestamp()) : "",
                        prescriberLabel,
                        source,
                        Boolean.TRUE.equals(userProfile.isFromHandicap()) ? "O" : "N",
                        Boolean.TRUE.equals(userProfile.handicapModeEnabled()) ? "O" : "N"
                });
            }

        } catch (IOException e) {
            log.error("Unable to generate handicap accounts CSV", e);
            throw new RuntimeException("Failed to generate handicap accounts export", e);
        }

        log.debug("Exported {} handicap accounts", handicapUsers.size());
        return writer.toString();
    }
}
