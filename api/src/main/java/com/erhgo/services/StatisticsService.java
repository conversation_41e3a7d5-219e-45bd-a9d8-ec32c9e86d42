package com.erhgo.services;

import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.*;
import com.erhgo.security.Role;
import com.erhgo.services.keycloak.KeycloakService;
import com.google.common.base.Joiner;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final AbstractOrganizationRepository organizationRepository;
    private final RecruitmentCandidatureRepository recruitmentCandidatureRepository;
    private final KeycloakService keycloakService;
    private final RecruitmentRepository recruitmentRepository;
    private final SpontaneousCandidatureRepository spontaneousCandidatureRepository;
    private final UserProfileRepository userProfileRepository;


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public MonthlyCandidaturesStatsDTO getMonthlyCandidaturesStats() {
        return new MonthlyCandidaturesStatsDTO()
                .rhoneAlpesStats(mapToMonthlyCandidaturesDTO(recruitmentCandidatureRepository.getMonthlyApplicationsStats(true)))
                .notRhoneAlpesStats(mapToMonthlyCandidaturesDTO(recruitmentCandidatureRepository.getMonthlyApplicationsStats(false)));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public MonthlySpontaneousCandidaturesStatsDTO getMonthlySpontaneousCandidaturesStats() {
        var stats = spontaneousCandidatureRepository.getMonthlySpontaneousCandidaturesStats();
        var dtoList = stats.stream()
                .map(stat -> new MonthlySpontaneousCandidaturesStatsItemDTO()
                        .month(stat.getMonth())
                        .totalSpontaneousCandidatureCount(stat.getTotalCandidatureCount()))
                .toList();
        return new MonthlySpontaneousCandidaturesStatsDTO()
                .spontaneousCandidaturesStats(dtoList);
    }


    private List<MonthlyCandidaturesDTO> mapToMonthlyCandidaturesDTO(List<RecruitmentCandidatureRepository.MonthlyCandidaturesDTO> rawStats) {
        return rawStats.stream()
                .map(raw -> new MonthlyCandidaturesDTO()
                        .month(raw.getMonth())
                        .averageCandidatureCount(raw.getAverageCandidatureCount())
                        .averageCandidatureCountExcludingZero(raw.getAverageCandidatureCountExcludingZero())
                        .totalRecruitmentCount(raw.getTotalRecruitmentCount())
                        .totalCandidatureCount(raw.getTotalCandidatureCount())
                        .totalCandidatureWithNotificationCount(raw.getTotalCandidatureWithNotificationCount())
                )
                .toList();
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public RecruitmentStatsDTO getRecruitmentStats() {
        log.debug("Retrieving recruitment stats");
        var recruitmentStats = recruitmentRepository.getRecruitmentStats();
        var sourcingPrefix = AbstractOrganization.OrganizationType.SOURCING.getPrefix();
        var projectPrefix = AbstractOrganization.OrganizationType.PROJECT.getPrefix();
        var rolesPerGroup = keycloakService.searchBOGroupsWithRoles(sourcingPrefix);
        var projectsCodes = rolesPerGroup.values().stream().flatMap(Collection::stream).filter(a -> StringUtils.startsWith(a, projectPrefix)).toList();
        var projectPerCodes = organizationRepository.findByCodeIn(projectsCodes).stream().collect(Collectors.toMap(AbstractOrganization::getCode, Function.identity()));
        var organizations = recruitmentStats.stream()
                .map(data -> new RecruitmentStatsItemDTO()
                        .organizationTitle(data.getOrganizationTitle())
                        .organizationCode(data.getOrganizationCode())
                        .lastRecruitmentPublicationDate(data.getLastRecruitmentPublicationDate() != null ? OffsetDateTime.ofInstant(data.getLastRecruitmentPublicationDate(), ZoneId.systemDefault()) : null)
                        .totalCandidatures(data.getTotalCandidatures())
                        .totalUsersInChannel(data.getTotalUsersInChannel())
                        .totalCandidaturesArchived(data.getTotalCandidaturesArchived())
                        .spontaneousCandidaturesArchived(data.getSpontaneousCandidaturesArchived())
                        .openRecruitmentsCount(data.getOpenRecruitmentsCount())
                        .closedRecruitmentsCount(data.getClosedRecruitmentsCount())
                        .totalTransmittedCandidatures(data.getTotalTransmittedCandidatures())
                        .selectedCandidaturesCount(data.getSelectedCandidaturesCount())
                        .contactedCandidaturesCount(data.getContactedCandidaturesCount())
                        .refusedCandidaturesCount(data.getRefusedCandidaturesCount())
                        .spontaneousCandidaturesCount(data.getSpontaneousCandidaturesCount())
                        .connectedAts(data.getConnectedAts())
                        .projects(getProjects(data, rolesPerGroup, projectPerCodes))
                )
                .toList();

        log.debug("Retrieved {} recruitment stats", organizations.size());
        return new RecruitmentStatsDTO().organizations(organizations);
    }

    private String getProjects(com.erhgo.repositories.dto.RecruitmentStatsDTO data, Map<String, Set<String>> rolesPerGroup, Map<String, AbstractOrganization> projectPerCodes) {
        var rolesForGroup = rolesPerGroup.get(data.getOrganizationCode());
        var none = "<aucun>";
        if (rolesForGroup == null) {
            return none;
        }
        var projectTitlesForGroup = rolesForGroup.stream()
                .filter(a -> a.startsWith(AbstractOrganization.OrganizationType.PROJECT.getPrefix()))
                .map(projectPerCodes::get)
                .filter(Objects::nonNull)
                .map(AbstractOrganization::getTitle)
                .toList();
        if (projectTitlesForGroup.isEmpty()) {
            return none;
        }
        return Joiner.on(", ").join(projectTitlesForGroup);
    }


    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public SeveralMetricsStatsDTO getSeveralMetricsStats() {
        return new SeveralMetricsStatsDTO()
                .userProfilesWithModifiedAttitudeCount(userProfileRepository.countUserProfilesWithModifiedAttitude())
                .userProfilesWithIsFromHandicapCount(userProfileRepository.countUserProfilesWithIsFromHandicap())
                .userProfilesWithHandicapModeEnabledCount(userProfileRepository.countUserProfilesWithHandicapModeEnabled())
                .candidaturesByHandicapEnabledUsersCount(userProfileRepository.countCandidaturesByHandicapEnabledUsers());
    }
}
