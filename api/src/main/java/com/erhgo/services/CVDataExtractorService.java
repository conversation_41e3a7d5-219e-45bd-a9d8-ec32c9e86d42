package com.erhgo.services;

import com.erhgo.domain.criteria.CriteriaValue;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.enums.FileImportState;
import com.erhgo.domain.exceptions.FileImportRateLimitReachedException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.exceptions.UnsupportedMediaTypeException;
import com.erhgo.domain.userprofile.GeneralInformation;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserRegistrationState;
import com.erhgo.openapi.dto.*;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.services.criteria.CriteriaService;
import com.erhgo.services.generation.*;
import com.erhgo.services.generation.dto.UserExperienceExtractionResponse;
import com.erhgo.services.generation.dto.UserInfosExtractionResponse;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.mailing.ResumeBackuperService;
import com.erhgo.services.notifier.OccupationCreationSourceType;
import com.erhgo.services.search.GeoService;
import com.erhgo.services.userprofile.FilePartProvider;
import com.erhgo.services.userprofile.UserProfileProvider;
import com.erhgo.utils.ImageUtils;
import com.erhgo.utils.PDFUtils;
import com.erhgo.utils.dto.ImageFileData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@RequiredArgsConstructor
public class CVDataExtractorService {
    private final UserProfileProvider userProfileProvider;
    private final ExperiencesFromCVGenerationService experiencesExtractorService;
    private final HardSkillsFromCVGenerationService hardSkillsFromCVGenerationService;
    private final HardSkillsFromImageCVGenerationService hardSkillsFromImageCVGenerationService;
    private final ExperiencesFromImageCVGenerationService experiencesImageExtractorService;
    private final UserInfosFromCVGenerationService userInfosExtractorService;
    private final UserInfosFromImageCVGenerationService userInfosImageExtractorService;
    private final UserExperienceService service;
    private final TransactionTemplate transactionTemplate;
    private final ApplicationContext applicationContext;
    private final GenerationReportLoggerService generationReportLoggerService;
    private final GeneralInformationService generalInformationService;
    private final CriteriaService criteriaService;
    private final KeycloakService keycloakService;
    private final GeoService geoService;
    private final OccupationForLabelGenerationService erhgoOccupationImporterService;
    private final ResumeBackuperService resumeBackuperService;

    private static final long MAX_FILE_SIZE = 10L * 1024 * 1024; // 10 Mo
    private static final int MAX_WORDS_ALLOWED = 6000;

    public UserFileImportStateDTO getFileImportState(String userId) {
        var user = getUserProfileOrCreate(userId);
        if (user.hasImportReachedRateLimit()) {
            return UserFileImportStateDTO.RATE_LIMIT_REACHED;
        }

        return UserFileImportStateDTO.fromValue(user.getFileImportState().name());
    }

    private UserProfile getUserProfileOrCreate(String userId) {
        return userProfileProvider.getUserProfileOrCreate(userId);
    }

    CompletableFuture<Void> processPdfFile(String userId, FilePartProvider file) {
        var pdfContent = PDFUtils.extractTextFromPDF(file);
        return applicationContext.getBean(this.getClass()).processUserFileContent(userId, pdfContent, false);
    }

    CompletableFuture<Void> processImageFile(String userId, FilePartProvider file) {
        try {
            var compressedImage = ImageUtils.compressImage(file);
            var imageData = new ImageFileData(compressedImage, MediaType.parseMediaType("image/jpeg"));
            return applicationContext.getBean(this.getClass()).processUserFileContent(userId, imageData, true);
        } catch (IOException e) {
            log.error("Error reading image file for user {}", userId, e);
            throw new GenericTechnicalException("Error processing image file", e);
        }
    }

    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public CompletableFuture<Void> extractUserExperiencesFromCV(String userId, FilePartProvider file) {
        if (file == null || !file.isValid() || file.contentType() == null) {
            log.error("Uploaded file by user {} is empty", userId);
            throw new GenericTechnicalException("File is empty");
        }

        if (file.getSize() > MAX_FILE_SIZE) {
            log.error("Uploaded file by user {} is too big", userId);
            throw new GenericTechnicalException("File size exceeds the maximum allowed size of %d MB".formatted(MAX_FILE_SIZE));
        }

        var user = getUserProfileOrCreate(userId);

        if (!user.canImportFile()) {
            log.error("User {} can't import file for the moment", userId);
            throw new FileImportRateLimitReachedException(userId);
        }
        resumeBackuperService.backupResume(userId, file);
        var contentType = file.contentType();
        var isPdf = ImageUtils.isPdf(file);
        var isImage = ImageUtils.isImage(file);
        if (isPdf || isImage) {
            setUserFileImportState(userId, FileImportState.WAITING);
            if (isPdf) {
                log.info("Processing PDF file for user {}, size: {}", userId, file.getSize());
                return processPdfFile(userId, file);
            } else {
                log.info("Processing image file for user {} with content type {} and size {}", userId, contentType, file.getSize());
                return processImageFile(userId, file);
            }
        } else {
            throw new UnsupportedMediaTypeException(file.contentType());
        }
    }

    @Async
    public CompletableFuture<Void> processUserFileContent(String userId, Object content, boolean isImage) {
        var allReports = new ArrayList<GenerationReportItemDTO>();
        try {
            setUserFileImportState(userId, FileImportState.ONGOING);

            if (isImage && content instanceof ImageFileData file) {
                extractHardSkills(userId, file, allReports);
                extractUserInfos(userId, file, allReports);
                extractUserExperiences(userId, file, allReports);
            } else if (!isImage && content instanceof String textContent) {
                String processableContent = prepareTextContent(textContent);

                if (!processableContent.isEmpty()) {
                    extractHardSkills(userId, processableContent, allReports);
                    extractUserInfos(userId, processableContent, allReports);
                    extractUserExperiences(userId, processableContent, allReports);
                } else {
                    log.warn("Empty text content for user {}", userId);
                }
            } else {
                log.warn("Unsupported content type for user {}: {}", userId, content.getClass().getName());
            }
        } catch (RuntimeException e) {
            log.error("Error processing file content for user {}", userId, e);
            throw new GenericTechnicalException("Error processing file content", e);
        } finally {
            generationReportLoggerService.logForReports("CV parse ended for user %s".formatted(userId), allReports);
            updateImportStateIfNeeded(userId);
        }
        return CompletableFuture.completedFuture(null);
    }

    private String prepareTextContent(String content) {
        String trimmedContent = content.trim();
        return trimmedContent.length() > MAX_WORDS_ALLOWED ?
                trimmedContent.substring(0, MAX_WORDS_ALLOWED) : trimmedContent;
    }

    private void updateImportStateIfNeeded(String userId) {
        transactionTemplate.execute(useless -> {
            var userLocal = getUserProfileOrCreate(userId);
            var state = userLocal.userFileImportState().state();

            if (state == FileImportState.COMPLETED) {
                var currentUserRegistrationStep = userLocal.registrationStep();
                if (currentUserRegistrationStep == null || currentUserRegistrationStep.ordinal() < UserRegistrationState.RegistrationStep.CONFIRMED_CONTACT_DETAILS.ordinal()) {
                    userLocal.updateRegistrationState(UserRegistrationState.RegistrationStep.CREATED_FROM_RESUME);
                    log.info("Import completed for user {}, setting user registration state to CREATED_FROM_RESUME", userId);
                }
            } else {
                userLocal.setUserFileImportState(FileImportState.ERROR);
            }
            return null;
        });
    }

    private void extractHardSkills(String userId, Object processableContent, List<GenerationReportItemDTO> allReports) {
        var hardSkills = processableContent instanceof ImageFileData file ?
                hardSkillsFromImageCVGenerationService.generateHardSkills(file) :
                hardSkillsFromCVGenerationService.generateHardSkills((String) processableContent);
        allReports.add(new GenerationReportItemDTO()
                .title("Extraction CV - hard skills")
                .durationInMs(hardSkills.getTotalDurationInMs())
                .nbTry(hardSkills.getNbTry())
                .success(hardSkills.isSuccess())
                .model(hardSkills.getModel())
        );
        transactionTemplate.execute(useless -> {
            getUserProfileOrCreate(userId).setHardSkills(hardSkills.getResult());
            return null;
        });
    }

    private void extractUserExperiences(String userId, Object processableContent, List<GenerationReportItemDTO> allReports) {
        var experiences = processableContent instanceof ImageFileData file ?
                experiencesImageExtractorService.generateUserExperiences(file) :
                experiencesExtractorService.generateUserExperiences((String) processableContent);
        allReports.add(new GenerationReportItemDTO()
                .title("Extraction CV - Experiences")
                .durationInMs(experiences.getTotalDurationInMs())
                .nbTry(experiences.getNbTry())
                .success(experiences.isSuccess())
                .model(experiences.getModel())
        );
        allReports.addAll(buildAndSaveUserExperiences(experiences.getResult(), userId));
    }

    private void extractUserInfos(String userId, Object processableContent, List<GenerationReportItemDTO> allReports) {
        var userInfos = processableContent instanceof ImageFileData file ?
                userInfosImageExtractorService.generateUserInfos(file) :
                userInfosExtractorService.generateUserInfos((String) processableContent);
        buildAndSaveUserInfos(userInfos.getResult(), userId);
        allReports.add(new GenerationReportItemDTO()
                .title("Extraction CV - Infos")
                .durationInMs(userInfos.getTotalDurationInMs())
                .nbTry(userInfos.getNbTry())
                .success(userInfos.isSuccess())
                .model(userInfos.getModel())
        );
    }

    private void setUserFileImportState(String userId, FileImportState state) {
        transactionTemplate.execute(useless -> {
            getUserProfileOrCreate(userId).setUserFileImportState(state);
            return null;
        });
    }

    private void buildAndSaveUserInfos(UserInfosExtractionResponse userInfos, String userId) {
        var userRepresentation = keycloakService.getFrontOfficeUserProfile(userId).orElseThrow();
        var generalInformations = Optional.ofNullable(getUserProfileOrCreate(userId).generalInformation());
        var location = Optional.ofNullable(userInfos.getLocation()).map(x -> geoService.fetchGeoCoordinates(x, "cv parsing")).orElse(null);

        generalInformationService.updateContactInfo(userId, new SaveUserContactInfoCommandDTO()
                .lastName(Optional.ofNullable(userRepresentation.getLastName()).map(StringUtils::trimToNull).orElse(userInfos.getLastName()))
                .firstName(Optional.ofNullable(userRepresentation.getFirstName()).map(StringUtils::trimToNull).orElse(userInfos.getFirstName()))
                .email(userRepresentation.getEmail())
                .location(generalInformations.map(GeneralInformation::getLocation).map(Location::buildDTO).orElse(location))
                .phoneNumber(generalInformations.map(GeneralInformation::getPhoneNumber).map(StringUtils::trimToNull).orElse(userInfos.getPhoneNumber()))
        );

        if (!userRepresentation.getEmail().equals(userInfos.getEmail())) {
            log.warn("Different email extracted ({}) from cv for user {} ({})", userInfos.getEmail(), userId, userRepresentation.getEmail());
        }

        List<String> criteriaValues = new ArrayList<>();
        if (userInfos.getDiplomaLevel() != null) {
            criteriaValues.add(CriteriaValue.getValueCodeForDiplomaLevel(userInfos.getDiplomaLevel()));
        }
        if (BooleanUtils.isTrue(userInfos.getDriverLicense())) {
            criteriaValues.add(CriteriaValue.getValueCodeForDriverLicence(DriverLicence.LICENCE_B));
        }

        if (!criteriaValues.isEmpty()) {
            criteriaService.setUserCriteria(new SaveUserCriteriasCommandDTO().userId(userId).selectedValueCodes(criteriaValues));
        }
    }

    private Collection<GenerationReportItemDTO> buildAndSaveUserExperiences(List<UserExperienceExtractionResponse> experiences, String userId) {
        var generatedExperiences = experiences.stream()
                .map(x -> new SaveExperienceCommandDTO()
                        .userId(userId)
                        .experienceId(UUID.randomUUID())
                        .experienceType(ExperienceTypeDTO.fromValue(x.getType().name()))
                        .jobTitle(x.getTitle())
                        .organizationName(x.getOrganizationTitle())
                        .duration(x.getDuration())
                ).toList();

        log.debug("About to create {} experiences from CV: {}", generatedExperiences.size(), generatedExperiences);

        if (generatedExperiences.size() > 15) {
            log.warn("More than 15 ({}) experiences found from CV, only 15 first are kept: {}", generatedExperiences.size(), generatedExperiences);
        }

        var allReports = generatedExperiences.stream().limit(15).flatMap(x -> {
            var result = erhgoOccupationImporterService.createOrUpdateOccupation(x.getJobTitle(), OccupationCreationSourceType.FROM_CV);
            if (!result.inError()) {
                x.setErhgoOccupationId(result.uuid());
            }
            service.saveExperience(x, false);
            return result.reports().stream();
        }).toList();

        transactionTemplate.execute(useless -> {
            getUserProfileOrCreate(userId).setUserFileImportState(FileImportState.COMPLETED);
            return null;
        });
        return allReports;
    }

}
