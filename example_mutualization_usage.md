# Exemple d'utilisation des méthodes mutualisées

## Avant (code dupliqué dans GeneralInformationService)

```java
// Dans GeneralInformationService.buildUserExportLine() ligne 418-421
if (securityService.isAdmin()) {
    var prescriberCode = userProfile.map(up -> up.prescriber().channel());
    var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
    var prescriberLabel = prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse(NONE);
    datas.add(prescriberLabel);
}

// Dans GeneralInformationService.buildUserExportLine() ligne 424-426
userProfile.map(UserProfile::channelSourceType)
        .map(UserChannel.ChannelSourceType::text)
        .orElse(UserChannel.ChannelSourceType.UNKNOWN.text())
```

## Après (utilisation des méthodes mutualisées)

```java
// Dans GeneralInformationService.buildUserExportLine() - version refactorisée
if (securityService.isAdmin()) {
    var prescriberLabel = userProfile.map(up -> up.getPrescriberLabel(organizationTitleForCode)).orElse("Aucun");
    datas.add(prescriberLabel);
}

// Pour la source
userProfile.map(UserProfile::getChannelSourceTypeText).orElse("Inconnu")
```

## Avantages de la mutualisation

1. **Réduction de la duplication** : Le code de formatage n'est plus dupliqué
2. **Maintenance facilitée** : Un seul endroit à modifier si la logique change
3. **Consistance** : Même formatage partout dans l'application
4. **Testabilité** : Les méthodes peuvent être testées unitairement
5. **Réutilisabilité** : Peut être utilisé dans d'autres services/exports

## Méthodes ajoutées dans UserProfile

```java
/**
 * Mutualisation du code de formatage du prescripteur
 * Utilisé dans GeneralInformationService et potentiellement ailleurs
 */
public String getPrescriberLabel(Map<String, String> organizationTitleForCode) {
    var prescriberCode = Optional.ofNullable(prescriber().channel());
    var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
    return prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse("Aucun");
}

/**
 * Mutualisation du code de formatage de la source
 * Utilisé dans GeneralInformationService et potentiellement ailleurs
 */
public String getChannelSourceTypeText() {
    return Optional.ofNullable(prescriber().channelSourceType())
            .map(UserChannel.ChannelSourceType::text)
            .orElse(UserChannel.ChannelSourceType.UNKNOWN.text());
}
```

## Utilisation dans d'autres contextes

Ces méthodes peuvent maintenant être utilisées dans :
- Tous les exports CSV
- Les affichages dans le BO
- Les rapports
- Les notifications
- Tout autre endroit nécessitant le formatage du prescripteur ou de la source
