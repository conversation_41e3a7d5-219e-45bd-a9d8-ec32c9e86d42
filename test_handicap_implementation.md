# Handicap Metrics Implementation Summary

## Changes Made

### 1. Repository Layer (UserProfileRepository.java)
- Added `countUserProfilesWithIsFromHandicap()` - counts accounts flagged handicap (origine)
- Added `countUserProfilesWithHandicapModeEnabled()` - counts accounts with handicap mode enabled (choix)
- Added `countCandidaturesByHandicapEnabledUsers()` - counts candidatures by handicap-enabled users
- Added `findHandicapUsers()` - finds all users with handicap flags for export

### 2. OpenAPI Definition (SeveralMetricsStats.yaml)
- Added `userProfilesWithIsFromHandicapCount` field
- Added `userProfilesWithHandicapModeEnabledCount` field
- Added `candidaturesByHandicapEnabledUsersCount` field

### 3. Service Layer (StatisticsService.java)
- Updated `getSeveralMetricsStats()` to include new handicap metrics
- Added `exportHandicapAccountsData()` method for CSV export with 6 columns:
  - Identifiant (userId)
  - Date de création (creation date)
  - Prescripteur (prescriber info with code and name)
  - Source (channel source type)
  - jenesuispasunhandicap origine O/N (isFromHandicap)
  - jenesuispasunhandicap actif O/N (handicapModeEnabled)

### 4. Controller Layer (StatisticsController.java)
- Added `/statistics/handicap-accounts/export` endpoint for CSV download

### 5. Frontend (SeveralMetricsStats.vue)
- Added display for all 3 new handicap metrics with appropriate icons
- Added export button for handicap data
- Updated info dialog with descriptions of all metrics

## Key Features

### Metrics Display
- **Comptes flagués handicap (origine)**: Shows count of accounts created via handicap system
- **Comptes handicap (choix)**: Shows count of accounts with handicap mode enabled
- **Candidatures portées par les comptes handicap actifs**: Shows candidatures from handicap-enabled users

### Export Functionality
- CSV export with proper French headers
- Includes prescriber information with organization name and code
- Shows O/N format for boolean handicap flags
- Proper date formatting (dd/MM/yyyy HH:mm:ss)

### Data Integrity
- Uses proper JPA queries for efficient data retrieval
- Handles null values appropriately
- Follows existing codebase patterns and conventions

## Testing
- Created basic unit test for StatisticsService
- Verified UserProfile field access patterns
- Confirmed DTO generation includes new fields

## Next Steps
1. Test the implementation in a development environment
2. Verify the export functionality works correctly
3. Ensure proper permissions are in place (ODAS_ADMIN role required)
4. Consider adding integration tests for the export functionality
