# Handicap Metrics Implementation Summary

## Changes Made

### 1. Repository Layer (UserProfileRepository.java)
- Added `countUserProfilesWithIsFromHandicap()` - counts accounts flagged handicap (origine)
- Added `countUserProfilesWithHandicapModeEnabled()` - counts accounts with handicap mode enabled (choix)
- Added `countCandidaturesByHandicapEnabledUsers()` - counts candidatures by handicap-enabled users

### 2. OpenAPI Definition (SeveralMetricsStats.yaml)
- Added `userProfilesWithIsFromHandicapCount` field
- Added `userProfilesWithHandicapModeEnabledCount` field
- Added `candidaturesByHandicapEnabledUsersCount` field

### 3. Service Layer (StatisticsService.java)
- Updated `getSeveralMetricsStats()` to include new handicap metrics

### 4. Domain Layer (UserProfile.java) - Code Mutualization
- Added `getPrescriberLabel(Map<String, String> organizationTitleForCode)` - mutualizes prescriber formatting logic
- Added `getChannelSourceTypeText()` - mutualizes source formatting logic
- These methods can now be reused across the codebase instead of duplicating the formatting logic

### 5. Frontend (SeveralMetricsStats.vue)
- Added display for all 3 new handicap metrics with appropriate icons
- Updated info dialog with descriptions of all metrics

## Key Features

### Metrics Display
- **Comptes flagués handicap (origine)**: Shows count of accounts created via handicap system
- **Comptes handicap (choix)**: Shows count of accounts with handicap mode enabled
- **Candidatures portées par les comptes handicap actifs**: Shows candidatures from handicap-enabled users

### Code Mutualization
- **Prescriber formatting**: Centralized in `UserProfile.getPrescriberLabel()` method
- **Source formatting**: Centralized in `UserProfile.getChannelSourceTypeText()` method
- **Reusability**: These methods can be used anywhere in the codebase instead of duplicating the logic
- **Consistency**: Ensures consistent formatting across all exports and displays

### Data Integrity
- Uses proper JPA queries for efficient data retrieval
- Handles null values appropriately
- Follows existing codebase patterns and conventions

## Testing
- Created comprehensive unit tests for StatisticsService handicap metrics
- Added tests for the new mutualized methods in UserProfile
- Confirmed DTO generation includes new fields

## Code Examples

### Using the mutualized methods:
```java
// Instead of duplicating this code everywhere:
var prescriberCode = userProfile.map(up -> up.prescriber().channel());
var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
var prescriberLabel = prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse("Aucun");

// Now you can simply use:
var prescriberLabel = userProfile.getPrescriberLabel(organizationTitleForCode);

// And for source:
var source = userProfile.getChannelSourceTypeText();
```

## Next Steps
1. Test the implementation in a development environment
2. Ensure proper permissions are in place (ODAS_ADMIN role required)
3. Verify the metrics display correctly in the BO interface
4. Consider refactoring existing code to use the new mutualized methods
