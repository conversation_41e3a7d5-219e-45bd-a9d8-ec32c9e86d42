<template>
  <div>

  <v-card>
    <v-card-title primary-title>
      <h3>
        <v-icon large color="primary">mdi-comment-plus-outline</v-icon>
        {{ $t('statistics.severalMetricsStats') }}
        <v-icon medium color="green" @click="showInfoDialog = true">mdi-information</v-icon>
      </h3>
    </v-card-title>

    <v-card-text>
      <v-alert
        v-if="loading"
        type="info"
        prominent
      >
        <v-icon left size="30">mdi-loading mdi-spin</v-icon>
        Loading...
      </v-alert>
      <div v-else-if="severalMetricsStats">
        <v-alert
          type="info"
          prominent
          class="mb-4"
        >
          <v-icon left size="30">mdi-file-document-edit-outline</v-icon>
          Utilisateurs ayant modifié leurs attitudes :
          <span class="text-h6 font-weight-bold">{{ severalMetricsStats.userProfilesWithModifiedAttitudeCount }}</span>
        </v-alert>

        <v-alert
          type="success"
          prominent
          class="mb-4"
        >
          <v-icon left size="30">mdi-wheelchair-accessibility</v-icon>
          Comptes flagués handicap (origine) :
          <span class="text-h6 font-weight-bold">{{ severalMetricsStats.userProfilesWithIsFromHandicapCount }}</span>
        </v-alert>

        <v-alert
          type="success"
          prominent
          class="mb-4"
        >
          <v-icon left size="30">mdi-account-check</v-icon>
          Comptes handicap (choix) :
          <span class="text-h6 font-weight-bold">{{ severalMetricsStats.userProfilesWithHandicapModeEnabledCount }}</span>
        </v-alert>

        <v-alert
          type="warning"
          prominent
          class="mb-4"
        >
          <v-icon left size="30">mdi-file-document-multiple</v-icon>
          Candidatures portées par les comptes handicap actifs :
          <span class="text-h6 font-weight-bold">{{ severalMetricsStats.candidaturesByHandicapEnabledUsersCount }}</span>
        </v-alert>

        <v-btn
          color="primary"
          @click="exportHandicapData"
          class="mt-4"
        >
          <v-icon left>mdi-download</v-icon>
          Exporter les données handicap
        </v-btn>
      </div>
    </v-card-text>

  </v-card>
    <v-dialog v-model="showInfoDialog" max-width="60%">
      <v-card>
        <v-card-title>
          <span class="text-h5">Informations sur les calculs</span>
        </v-card-title>
        <v-card-text>
          <p>Ces indicateurs nous donnent des informations sur les utilisateurs et leurs interactions avec le système&nbsp;:</p>
          <ul>
            <li>
              <strong>Utilisateurs ayant modifié leur attitude&nbsp;:</strong> Nombre de personnes ayant modifié l'attitude générée par LLM (typiquement&nbsp;: sur le FO depuis la liste des expériences).
            </li>
            <li>
              <strong>Comptes flagués handicap (origine)&nbsp;:</strong> Nombre de comptes créés via le système handicap (isFromHandicap = true).
            </li>
            <li>
              <strong>Comptes handicap (choix)&nbsp;:</strong> Nombre de comptes ayant activé le mode handicap (handicapModeEnabled = true).
            </li>
            <li>
              <strong>Candidatures portées par les comptes handicap actifs&nbsp;:</strong> Nombre de candidatures soumises par des utilisateurs ayant le mode handicap activé.
            </li>
          </ul>
        </v-card-text>
        <v-card-actions>
          <v-spacer/>
          <v-btn color="primary" @click="showInfoDialog = false">Fermer</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import StatisticsService from './StatisticsService';

export default {
  name: 'SeveralMetricsStats',
  async created() {
    this.statisticsService = new StatisticsService();
    this.loading = true;
    await this.statisticsService.fetchSeveralMetricsStats();
    this.loading = false;
  },
  data() {
    return {
      statisticsService: null,
      showInfoDialog: false,
      loading: false,
    };
  },
  computed: {
    severalMetricsStats() {
      return this.statisticsService?.severalMetricsStats;
    },
  },
  methods: {
    async exportHandicapData() {
      try {
        const response = await this.$http.get('/api/odas/statistics/handicap-accounts/export', {
          responseType: 'blob',
        });

        const blob = new Blob([response.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'handicap-accounts-export.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$toast.success('Export des données handicap réussi');
      } catch (error) {
        console.error('Error exporting handicap data:', error);
        this.$toast.error('Erreur lors de l\'export des données handicap');
      }
    },
  },
};
</script>

<style lang="scss">
.spontaneous-candidatures-stats {
  .stats tr:nth-child(even) {
    background-color: #ECEFF1;
  }
}
</style>
